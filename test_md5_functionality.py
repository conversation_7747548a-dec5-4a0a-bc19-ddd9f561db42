#!/usr/bin/env python3
"""
测试MD5功能的脚本
"""

import sys
import os
from pathlib import Path

# 添加backend目录到Python路径
backend_dir = Path(__file__).parent / "backend"
sys.path.insert(0, str(backend_dir))

from utilities.utils import calculate_md5
from api.system_api import SystemAPI

def test_md5_calculation():
    """测试MD5计算功能"""
    print("=== 测试MD5计算功能 ===")
    
    # 创建一个测试文件
    test_file = Path("test_file.txt")
    test_content = "Hello, World! This is a test file for MD5 calculation."
    
    try:
        # 写入测试内容
        with open(test_file, 'w', encoding='utf-8') as f:
            f.write(test_content)
        
        # 计算MD5
        md5_hash = calculate_md5(str(test_file))
        print(f"测试文件MD5: {md5_hash}")
        
        # 验证MD5是否一致
        md5_hash2 = calculate_md5(str(test_file))
        assert md5_hash == md5_hash2, "MD5计算结果不一致"
        print("✓ MD5计算功能正常")
        
    except Exception as e:
        print(f"✗ MD5计算功能异常: {e}")
    finally:
        # 清理测试文件
        if test_file.exists():
            test_file.unlink()

def test_model_status_api():
    """测试模型状态API"""
    print("\n=== 测试模型状态API ===")
    
    try:
        system_api = SystemAPI()
        result = system_api.get_model_status()
        
        print(f"API返回状态码: {result.get('code')}")
        print(f"API返回数据: {result.get('data', {})}")
        
        if result.get('code') == 200:
            print("✓ 模型状态API调用成功")
            
            # 检查返回的状态值
            data = result.get('data', {})
            for model_name, status in data.items():
                print(f"  {model_name}: {status}")
                
        else:
            print(f"✗ 模型状态API调用失败: {result.get('msg')}")
            
    except Exception as e:
        print(f"✗ 模型状态API异常: {e}")

def test_remove_model_api():
    """测试移除模型API"""
    print("\n=== 测试移除模型API ===")
    
    try:
        system_api = SystemAPI()
        
        # 测试移除一个不存在的模型
        result = system_api.remove_model("test_model.onnx")
        print(f"移除不存在模型的结果: {result}")
        
        # 测试移除一个已知的模型名称（但可能不存在文件）
        result = system_api.remove_model("m_repair_tiny.onnx")
        print(f"移除m_repair_tiny.onnx的结果: {result}")
        
    except Exception as e:
        print(f"✗ 移除模型API异常: {e}")

if __name__ == "__main__":
    print("开始测试模型管理功能...")
    
    test_md5_calculation()
    test_model_status_api()
    test_remove_model_api()
    
    print("\n测试完成！")
