<template>
  <div class="page-header">
    <div class="header-content">
      <div class="title-group">
        <h2 class="title">{{ title }}</h2>
        <div v-if="tags" class="tags-group">
          <el-tag
            v-for="tag in tags"
            :key="tag"
            size="small"
            type="success"
            effect="light"
            class="tag-item"
          >
            {{ tag }}
          </el-tag>
        </div>
      </div>
      <div class="description">
        <span class="description-text">{{ description }}</span>
      </div>
    </div>
  </div>
</template>

<script setup>
defineProps({
  title: {
    type: String,
    required: true
  },
  description: {
    type: String,
    required: true
  },
  tags: {
    type: Array,
    default: () => []
  }
})
</script>

<style scoped lang="scss">
.page-header {
  text-align: center;
  margin-bottom: 24px;

  .header-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    // gap: 16px;

    .title-group {
      display: flex;
      flex-direction: column;
      align-items: center;
      // gap: 12px;

      .title {
        font-size: 28px;
        color: var(--el-text-color-primary);
        margin: 0;
        font-weight: 500;
      }

      .tags-group {
        display: flex;
        gap: 8px;
        
        .tag-item {
          height: 22px;
          padding: 0 8px;
          font-size: 12px;
        }
      }
    }

    .description {
        margin-top: 8px;
      .description-text {
        font-size: 16px;
        color: var(--el-text-color-secondary);
        max-width: 800px;
      }
    }
  }
}
</style> 