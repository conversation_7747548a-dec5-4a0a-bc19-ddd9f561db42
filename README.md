# pywebview-base

pywebview和vite+vue3 的基础项目

使用了pdm和node


### ffmpeg

#### 安装

```
https://evermeet.cx/ffmpeg/

https://www.gyan.dev/ffmpeg/builds/#release-builds
```

ffmpeg 工具类在 backend/ai_tools/ffmpeg_tool.py 中

#### Windows隐藏控制台窗口解决方案

项目已实现完整的Windows隐藏控制台窗口解决方案：

- **自动隐藏**：所有FFmpeg操作在Windows上自动隐藏控制台窗口
- **装饰器支持**：使用 `@windows_no_console` 装饰器包装自定义函数
- **上下文管理器**：使用 `_hidden_console_context()` 进行临时操作
- **线程安全**：支持多线程环境使用
- **跨平台兼容**：在非Windows系统上无额外开销



## 打包流程

```shell
# mac intel 打包
pdm build-front

pdm build-mac

source .venv/bin/activate

dmgbuild -s intel_mac_dmg.py "灵象工具箱 v0.1.5" "[mac-intel]-灵象工具箱-V0.1.5.dmg"
dmgbuild -s m_mac_dmg.py "灵象工具箱 v0.1.5" "[mac-Apple]-灵象工具箱-V0.1.5.dmg"
```