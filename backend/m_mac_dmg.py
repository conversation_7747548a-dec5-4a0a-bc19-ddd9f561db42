# -*- coding: utf-8 -*-
# 应用名称
app_name = "灵象工具箱"

version = "V0.1.5"
# DMG 输出文件名
filename = f"[mac-Apple]-{app_name}-{version}.dmg"

# 设置卷名
volume_name = f"{app_name} {version} installer"

# 目标应用路径
app_path = "./dist/m1/灵象工具箱.app"

# 背景图片路径 - 取消注释并使用背景图
background = "./assets/bg.jpg"  # 需要创建这个背景图

# 应用程序图标
icon = "./assets/logo.icns"

# DMG 的体积大小，根据实际应用大小调整
# 放入 .app 文件和 Applications 链接
files = [
    (app_path, "灵象工具箱.app"),
    "./assets/灵象工具箱.webloc",
]

symlinks = {"Applications": "/Applications"}

# DMG 详细配置
window_rect = ((200, 200), (590, 416))  # 使窗口大小与背景图匹配

icon_locations = {
    "灵象工具箱.app": (160, 120),
    "Applications": (430, 120),
    "灵象工具箱.webloc": (450, 243),
}

icon_size = 60
text_size = 12
format = "UDBZ"
