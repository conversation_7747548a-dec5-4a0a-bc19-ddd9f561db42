# 构建
set -e

# 设置默认版本号，如果有参数则使用参数值
VERSION=${1:-"v0.1.5"}

echo "开始构建前端"
pdm build-front
echo "前端构建完成"

echo "开始构建后端"
pdm run python setup.py build_ext --inplace
echo "后端构建完成"

echo "开始打包"
# 检测芯片架构并选择合适的构建命令
if [[ $(uname -m) == "arm64" ]]; then
  echo "检测到 Apple 芯片，使用 build-m1"
  pdm build-m1
else
  echo "检测到 Intel 芯片，使用 build-mac"
  pdm build-mac
fi
echo "打包完成"

# 根据芯片架构选择合适的 DMG 名称
if [[ $(uname -m) == "arm64" ]]; then
  pdm run dmgbuild -s m_mac_dmg.py "灵象工具箱 ${VERSION}" "[mac-arm64]-灵象工具箱-${VERSION}.dmg"
else
  pdm run dmgbuild -s intel_mac_dmg.py "灵象工具箱 ${VERSION}" "[mac-intel]-灵象工具箱-${VERSION}.dmg"
fi 