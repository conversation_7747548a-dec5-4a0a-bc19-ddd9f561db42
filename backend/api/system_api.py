from utilities.license_verifier import SecureLicenseVerifier
from utilities.log import logger
from utilities.machine_code import get_machine_code as machine_code
from utilities.response import res200, res500
import webview
import time
import os
import json
import shutil
from typing import Union
from conf.config import config
from conf.setting import settings
from pathlib import Path
import platform
import traceback
from utilities.reomte_util import RemoteUtil
from utilities.machine_code import get_machine_code
from utilities.verify_email import validate_email
from utilities.utils import calculate_md5
import uuid


remote_util = RemoteUtil(settings.BADER_SERVER_URL)


class SystemAPI:
    name = "system"

    def __init__(self):
        pass

    def get_system_info(self):
        return {"name": "system", "version": "1.0.0"}

    def get_license_info(self):
        return SecureLicenseVerifier().get_license_info()

    def get_machine_code(self):
        try:
            return res200(data={"machine_code": machine_code()})
        except Exception as e:
            logger.error(f"Error getting machine code: {traceback.format_exc()}")
            return res500(str(e))

    def check_license_status(self):
        """检查授权状态"""
        license_dir = os.path.join(os.path.expanduser("~"), ".lx_cache", "licenses")
        if not os.path.exists(license_dir):
            os.makedirs(license_dir, exist_ok=True)

        _machine_code = machine_code()
        # 检查所有授权文件
        for filename in os.listdir(license_dir):
            if filename.endswith(".license"):
                file_path = os.path.join(license_dir, filename)
                try:
                    status, message = SecureLicenseVerifier().verify_license(
                        file_path, _machine_code
                    )
                    if not status:
                        try:
                            os.remove(file_path)
                        except Exception as e:
                            logger.error(
                                f"Error removing license file {file_path}: {str(e)}"
                            )
                    return res200(data={"status": status, "message": message})
                except Exception as e:
                    logger.error(f"Error verifying license {file_path}: {str(e)}")
                    return res500(str(e))
        return res200(data={"status": False, "message": "未授权"})

    def check_login_status(self):
        """检查登录状态"""
        return res200(data={"status": False, "message": ""})

    def verify_license(self, file_path):
        """上传授权文件"""
        try:
            license_dir = os.path.join(os.path.expanduser("~"), ".lx_cache", "licenses")
            os.makedirs(license_dir, exist_ok=True)

            # 获取文件名
            file_name = os.path.basename(file_path)
            if not file_name.endswith(".license"):
                return res500("无效的授权文件格式")

            _machine_code = machine_code()
            # 先判断下授权文件是否已经存在，且已过期，如果已过期，则删除
            for filename in os.listdir(license_dir):
                if filename.endswith(".license"):
                    file_path = os.path.join(license_dir, filename)
                    status, message = SecureLicenseVerifier().verify_license(
                        file_path, _machine_code
                    )
                    if not status:
                        os.remove(os.path.join(license_dir, file_name))

            # 复制文件到授权目录
            target_path = os.path.join(license_dir, file_name)
            shutil.copy2(file_path, target_path)

            # 验证新上传的授权文件
            status, message = SecureLicenseVerifier().verify_license(
                target_path, _machine_code
            )
            if not status:
                # 如果验证失败，删除已复制的文件
                os.remove(target_path)
            return res200(data={"status": status, "message": message})

        except Exception as e:
            logger.error(f"Error uploading license: {str(e)}")
            return res500(str(e))

    def get(self, params: Union[dict, None] = None):
        return res200(dict(config))

    def put(self, payload):
        if isinstance(payload, str):
            payload = json.loads(payload)
        for key, value in payload.items():
            if key in config:
                config.save(key, value)
        return res200(dict(config))

    def update_window_setting(self, payload):
        if isinstance(payload, str):
            payload = json.loads(payload)
        window = webview.windows[0]
        time.sleep(0.1)  # If we don't sleep, the window will get frozen. Have no idea.
        pin_window = payload.get("pin_window")
        if pin_window is not None:
            window.on_top = pin_window
            config.save("window.on_top", pin_window)
        return res200()

    def get_model_status(self):
        """获取所有模型的下载状态和MD5校验状态"""
        try:
            model_dir = Path(config.get("model_dir"))

            # 定义模型文件和对应的MD5值
            MODEL_MD5_MAP = {
                "rmbg2.onnx": "55913f5ac5540eb64a069204a8bee870",
                "segmentation_normal.onnx": "4fab47adc4ff364be1713e97b7e66334",
                "people_segmentation.onnx": "d0ded73e80bc0d38a64e2286486e4ea3",
                "m_repair_normal.onnx": "2777748dc5275b27dafc63c5d4f1f730",
                "real_general.onnx": "818723ba1416fb43472d514ebf064d3c",
                "rmbg14.onnx": "8bb9b16ff49cda31e7784852873cfd0d",
                "cls_model.onnx": "7754e7a0e5e29fea28ae06553200f943",
                "real_anime.onnx": "64ea72bbd999bde610b49b8032f7e8b9",
                "m_repair_tiny.onnx": "52c80ef189f79853164a14e4fee43474",
                "det_model.onnx": "f0f60c67c5862cf78c3f73074bd6475b",
                "segmentation_high.onnx": "95d7129b7abd6120b571e848f269a8ab",
                "rec_model.onnx": "a70b36b8c0de670acc2e6ec13d492977",
            }

            # FFmpeg文件的MD5值
            FFMPEG_MD5_MAP = {
                "ffmpeg": "178f2adb4b0de6039229a9844de5c5de",
                "ffprobe": "8138503e3e81f3d64ed547838ac83b9e",
                "ffmpeg.exe": "8fc3669a8dab286d84d55fde494cae8d",
                "ffprobe.exe": "e416b82c001e4852f1c517a002c1811b",
            }

            def check_file_status(file_path, expected_md5):
                """检查文件状态：不存在、MD5不匹配、正常"""
                if not file_path.exists():
                    return "missing"  # 文件不存在

                try:
                    actual_md5 = calculate_md5(str(file_path))
                    if actual_md5 == expected_md5:
                        return "valid"  # 文件存在且MD5正确
                    else:
                        return "invalid"  # 文件存在但MD5不匹配
                except Exception as e:
                    logger.error(f"Error calculating MD5 for {file_path}: {str(e)}")
                    return "error"  # 计算MD5时出错

            # 检查FFmpeg状态
            if platform.system() == "Windows":
                ffmpeg_dir = model_dir.joinpath("win_ffmpeg")
                ffmpeg_status = "valid"
                if not ffmpeg_dir.exists():
                    ffmpeg_status = "missing"
                else:
                    ffmpeg_file_status = check_file_status(
                        ffmpeg_dir.joinpath("ffmpeg.exe"),
                        FFMPEG_MD5_MAP["ffmpeg.exe"]
                    )
                    ffprobe_file_status = check_file_status(
                        ffmpeg_dir.joinpath("ffprobe.exe"),
                        FFMPEG_MD5_MAP["ffprobe.exe"]
                    )
                    if ffmpeg_file_status != "valid" or ffprobe_file_status != "valid":
                        ffmpeg_status = "invalid"

            elif platform.system() == "Darwin":
                ffmpeg_dir = model_dir.joinpath("mac_ffmpeg")
                ffmpeg_status = "valid"
                if not ffmpeg_dir.exists():
                    ffmpeg_status = "missing"
                else:
                    ffmpeg_file_status = check_file_status(
                        ffmpeg_dir.joinpath("ffmpeg"),
                        FFMPEG_MD5_MAP["ffmpeg"]
                    )
                    ffprobe_file_status = check_file_status(
                        ffmpeg_dir.joinpath("ffprobe"),
                        FFMPEG_MD5_MAP["ffprobe"]
                    )
                    if ffmpeg_file_status != "valid" or ffprobe_file_status != "valid":
                        ffmpeg_status = "invalid"
            else:
                ffmpeg_status = "missing"

            # 检查PPOCR模型状态
            ppocr_files = ["det_model.onnx", "rec_model.onnx", "cls_model.onnx"]
            ppocr_status = "valid"
            for file_name in ppocr_files:
                file_status = check_file_status(
                    model_dir.joinpath(file_name),
                    MODEL_MD5_MAP[file_name]
                )
                if file_status != "valid":
                    ppocr_status = file_status
                    break

            # 检查其他模型状态
            status = {}
            for model_name, expected_md5 in MODEL_MD5_MAP.items():
                if model_name not in ppocr_files:  # PPOCR文件已经单独处理
                    status[model_name] = check_file_status(
                        model_dir.joinpath(model_name),
                        expected_md5
                    )

            # 添加组合模型状态
            status["ppocr"] = ppocr_status
            status["ffmpeg"] = ffmpeg_status

            return res200(data=status)
        except Exception as e:
            logger.error(f"Error getting model status: {traceback.format_exc()}")
            return res500(str(e))

    def download_model(self, model_name):
        """下载指定模型"""
        try:
            from utilities.download_model import (
                download_migan,
                download_modnet,
                download_tiny_232,
                download_epoch_100,
                download_lama_fp32,
                download_ppocr,
                download_segmentation_quick,
                download_ffmpeg,
                download_real_general,
                download_real_anime,
            )

            download_funcs = {
                "m_repair_tiny.onnx": download_migan,
                "m_repair_normal.onnx": download_lama_fp32,
                "people_segmentation.onnx": download_modnet,
                "segmentation_quick.onnx": download_segmentation_quick,
                "segmentation_normal.onnx": download_tiny_232,
                "segmentation_high.onnx": download_epoch_100,
                "ppocr": download_ppocr,
                "ffmpeg": download_ffmpeg,
                "real_general.onnx": download_real_general,
                "real_anime.onnx": download_real_anime,
            }

            if model_name in download_funcs:
                download_funcs[model_name]()
                return res200(msg="下载成功")
            return res500("未知的模型名称")
        except Exception as e:
            return res500(str(e))

    def download_all_models(self):
        """一键下载所有模型"""
        try:
            from utilities.download_model import (
                download_migan,
                download_modnet,
                download_tiny_232,
                download_epoch_100,
                download_lama_fp32,
                download_ppocr,
                download_ffmpeg,
                download_real_general,
                download_real_anime,
            )

            download_funcs = [
                download_migan,
                download_modnet,
                download_tiny_232,
                download_epoch_100,
                download_lama_fp32,
                download_ppocr,
                download_ffmpeg,
                download_real_general,
                download_real_anime,
            ]

            for func in download_funcs:
                try:
                    func()
                except Exception as e:
                    logger.error(f"Error downloading model {func.__name__}: {str(e)}")
                    return res500(f"模型{func.__name__}下载失败")
            return res200(msg="所有模型下载成功")
        except Exception as e:
            return res500(str(e))

    def update_model_path(self, path):
        """更新模型存储路径"""
        try:
            if not os.path.exists(path):
                os.makedirs(path, exist_ok=True)
            config.save("model_dir", path)
            return res200(data={"folder_path": path})
        except Exception as e:
            return res500(str(e))

    def import_model(self, payload):
        """导入自定义模型"""
        try:
            if isinstance(payload, str):
                payload = json.loads(payload)
            model_name = payload.get("model_name")
            file_path = payload.get("file_path")
            # 模型大小以MB为单位
            model_size_mb = {
                "rmbg14.onnx": 175,
                "rmbg2.onnx": 1020,
            }
            target_size_mb = model_size_mb.get(model_name, 0)
            if target_size_mb == 0:
                return res500("未知的模型名称")

            # 获取文件大小（字节）并转换为MB
            file_size_bytes = os.path.getsize(file_path)
            file_size_mb = file_size_bytes / (1024 * 1024)

            # 允许5%的误差范围
            error_margin = 0.20
            min_size = target_size_mb * (1 - error_margin)
            max_size = target_size_mb * (1 + error_margin)

            if not (min_size <= file_size_mb <= max_size):
                return res500(f"模型文件大小不正确")

            model_dir = Path(config.get("model_dir"))
            target_path = model_dir.joinpath(model_name)

            # 验证文件是否为有效的ONNX模型
            if not file_path.lower().endswith(".onnx"):
                return res500("无效的模型文件格式，请选择.onnx文件")
            try:
                # 复制文件到模型目录
                shutil.copy2(file_path, target_path)
            except Exception as e:
                return res500(str(e))

            return res200(msg="模型导入成功")
        except Exception as e:
            logger.error(f"Error importing model: {str(e)}")
            return res500(str(e))

    def remove_model(self, model_name):
        """移除指定模型"""
        try:
            model_dir = Path(config.get("model_dir"))

            # 定义可移除的模型文件映射
            removable_models = {
                "m_repair_tiny.onnx": ["m_repair_tiny.onnx"],
                "m_repair_normal.onnx": ["m_repair_normal.onnx"],
                "people_segmentation.onnx": ["people_segmentation.onnx"],
                "segmentation_normal.onnx": ["segmentation_normal.onnx"],
                "segmentation_high.onnx": ["segmentation_high.onnx"],
                "rmbg14.onnx": ["rmbg14.onnx"],
                "rmbg2.onnx": ["rmbg2.onnx"],
                "real_general.onnx": ["real_general.onnx"],
                "real_anime.onnx": ["real_anime.onnx"],
                "ppocr": ["det_model.onnx", "rec_model.onnx", "cls_model.onnx"],
                "ffmpeg": []  # FFmpeg需要特殊处理
            }

            if model_name not in removable_models:
                return res500("未知的模型名称")

            # 处理FFmpeg特殊情况
            if model_name == "ffmpeg":
                if platform.system() == "Windows":
                    ffmpeg_dir = model_dir.joinpath("win_ffmpeg")
                    if ffmpeg_dir.exists():
                        shutil.rmtree(ffmpeg_dir)
                elif platform.system() == "Darwin":
                    ffmpeg_dir = model_dir.joinpath("mac_ffmpeg")
                    if ffmpeg_dir.exists():
                        shutil.rmtree(ffmpeg_dir)
                return res200(msg="FFmpeg移除成功")

            # 移除普通模型文件
            files_to_remove = removable_models[model_name]
            removed_files = []

            for file_name in files_to_remove:
                file_path = model_dir.joinpath(file_name)
                if file_path.exists():
                    os.remove(file_path)
                    removed_files.append(file_name)
                    logger.info(f"已移除模型文件: {file_path}")

            if removed_files:
                return res200(msg=f"模型移除成功，已删除文件: {', '.join(removed_files)}")
            else:
                return res200(msg="模型文件不存在，无需移除")

        except Exception as e:
            logger.error(f"Error removing model {model_name}: {str(e)}")
            return res500(f"移除模型失败: {str(e)}")

    def login(self, payload):
        """登录"""
        if isinstance(payload, str):
            payload = json.loads(payload)
        email = payload.get("email")
        password = payload.get("password")
        try:
            res = remote_util.login(email, password)
            if res.get("code") == 200:
                access_token = res.get("data").get("access_token")
                config.save("token", access_token)
                return res200(data={"token": access_token})
            else:
                return res500(res.get("msg"))
        except Exception as e:
            return res500(str(f"登录失败: 网络错误"))

    def register(self, payload):
        """注册"""
        if isinstance(payload, str):
            payload = json.loads(payload)
        email = payload.get("email")
        password = payload.get("password")
        invite_code = payload.get("invite_code")
        try:
            verify_ed, msg = validate_email(email)
        except Exception as e:
            pass
        try:
            machine_code = get_machine_code()
        except Exception as e:
            machine_code = str(uuid.uuid4())
        res = remote_util.register(email, password, invite_code, machine_code)
        if res.get("code") == 200:
            return res200(msg="注册成功")
        else:
            return res500(res.get("msg"))

    def logout(self):
        """退出"""
        config.save("token", "")
        return res200(msg="退出成功")

    def get_user_info(self):
        """获取用户信息"""
        try:
            res = remote_util.get_user_info(config.get("token"))
            if res.get("code") == 200:
                return res200(data=res.get("data"))
            else:
                config.save("token", "")
                return res500(res.get("msg"))
        except Exception as e:
            return res500(str(e))

    def get_plans(self):
        """获取套餐"""
        try:
            res = remote_util.get_plans(config.get("token"))
            if res.get("code") == 200:
                return res200(data=res.get("data"))
            else:
                return res500(res.get("msg"))
        except Exception as e:
            return res500(str(e))

    def get_orders(self, payload):
        """获取订单"""
        try:
            if isinstance(payload, str):
                payload = json.loads(payload)
            page = payload.get("page")
            page_size = payload.get("page_size")
            res = remote_util.get_orders(config.get("token"), page, page_size)
            if res.get("code") == 200:
                return res200(
                    data=res.get("data"),
                    total=res.get("total"),
                    page=page,
                    page_size=page_size,
                )
            else:
                return res500(res.get("msg"))
        except Exception as e:
            return res500(str(e))

    def create_order(self, payload):
        """创建订单"""
        try:
            if isinstance(payload, str):
                payload = json.loads(payload)
            res = remote_util.create_order(
                config.get("token"), payload.get("plan_id"), payload.get("pay_channel")
            )
            if res.get("code") == 200:
                return res200(data=res.get("data"))
            else:
                return res500(res.get("msg"))
        except Exception as e:
            return res500(str(e))

    def check_order_status(self, payload):
        try:
            if isinstance(payload, str):
                payload = json.loads(payload)
            res = remote_util.check_order_status(
                config.get("token"), payload.get("order_id")
            )
            if res.get("code") == 200:
                return res200(data=res.get("data"))
            else:
                return res500(res.get("msg"))
        except Exception as e:
            return res500(str(e))
