from ai_tools.rmbg import ImageSegmentation
from ai_tools.modnet import MODNetSegmentation
from ai_tools.brn import BiRefNetAPP


def get_segment_func(ai_model):
    """获取对应的分割函数"""
    if ai_model == "modnet":
        return MODNetSegmentation().segment_image
    elif ai_model == "fast":
        return BiRefNetAPP(model_type="tiny_232").segment_image
    elif ai_model == "quality":
        return BiRefNetAPP(model_type="epoch_100").segment_image
    elif ai_model == "rmbg2":
        return BiRefNetAPP(model_type="rmbg2").segment_image
    elif ai_model == "rmbg14":
        return ImageSegmentation().segment_image
    elif ai_model == "superfast":
        return ImageSegmentation().segment_image
