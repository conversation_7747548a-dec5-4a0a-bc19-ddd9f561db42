from utilities.response import res500, res200, res400
from utilities.convert_utils import (
    convert_image_format,
    can_convert_file,
    gif_to_image,
    get_file_base_name,
)
from utilities.log import logger
from ai_tools.utils import get_api_server_url
from utilities.utils import img_to_base64
import time
import os
from utilities.compress import compress_image, can_compress_file
from pathlib import Path


class CompressImageAPI:
    name = "compress_image"

    def single_compress_image(self, playload):
        try:
            input_path = playload["input_path"]
            quality = playload.get("quality", None)
        except KeyError:
            return res500("Invalid input")
        try:
            base_name = get_file_base_name(input_path)
            ext = Path(input_path).suffix
        except Exception as e:
            base_name = f"lxt"
        save_dir = os.path.dirname(input_path)
        filename = f"{base_name}-compress{ext}"
        save_path = os.path.join(save_dir, filename)
        compress_image(input_path, save_path, quality)
        return res200(data={"output_path": save_path})

    def get_folder_compress_images(self, folder_path: str) -> dict:
        """
        Get the list of images in the input folder.


        :param folder_path: The path of the input folder.

        :return: The response object with the list of images in the folder.

        """
        try:
            compress_images = []
            for root, dirs, files in os.walk(folder_path):
                for file in files:
                    if can_compress_file(file):
                        image_path = os.path.join(root, file)
                        image_url = (
                            f"{get_api_server_url()}/local_file?file_path={image_path}"
                        )
                        tem = {
                            "image_name": os.path.basename(image_path),
                            "image_path": image_path,
                            "image_url": image_url,
                            "status": "",
                            "compress_result": "",
                        }
                        compress_images.append(tem)
            return res200(
                data={"compress_images": compress_images, "folder_path": folder_path}
            )
        except Exception as e:
            logger.error(f"Error in get_folder_compress_images: {e} {folder_path}")
            return res500("Error in get_folder_compress_images")

    def compress_image_from_folder(self, playload):
        try:
            image_path = playload.get("image_path")
            folder_path = playload.get("folder_path")
            quality = playload.get("quality", None)
            save_folder = playload.get("save_folder")
            if not save_folder:
                save_folder = os.path.join(folder_path, "result")
            if not os.path.exists(save_folder):
                os.makedirs(save_folder, exist_ok=True)
            try:
                base_name = get_file_base_name(image_path)
                ext = Path(image_path).suffix
            except Exception as e:
                base_name = f"lxt-{int(time.time()*1000)}"
            filename = f"{base_name}-compress{ext}"
            save_path = os.path.join(save_folder, filename)
            try:
                original_size, new_size = compress_image(image_path, save_path, quality)
            except Exception as e:
                logger.error(f"compress image {image_path} to {save_path} failed: {e}")
                return res500(
                    msg=f"compress image {image_path} to {save_path} failed: {e}"
                )
            logger.info(f"compress image {image_path} to {save_path} success")
            return res200(
                data={
                    "compress_result": save_path,
                    "original_size": original_size,
                    "new_size": new_size,
                }
            )

        except Exception as e:
            logger.error(f"Error in compress image: {e} {image_path}")
            return res500("Error in compress image")
