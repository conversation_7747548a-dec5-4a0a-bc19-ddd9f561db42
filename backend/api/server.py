from fastapi import FastAP<PERSON>, HTTPException, Request
from fastapi.middleware.cors import CORSMiddleware
import uvicorn
from typing import Optional, Union
import threading
from pydantic import BaseModel, Field, field_validator
from pathlib import Path
from fastapi.openapi.docs import get_swagger_ui_html
from fastapi import applications
from fastapi.responses import StreamingResponse
import mimetypes
import os
import tempfile
import shutil
from urllib.parse import unquote
import os.path
import sys
import numpy as np
from fastapi.staticfiles import StaticFiles

try:
    from utilities.log import logger
    from utilities.utils import (
        image_obj_to_base64,
        is_image,
        save_bs64_image,
        add_stroke,
        crop_main_subject,
        read_image,
        get_image_format,
        process_gif,
    )
    from ai_tools.rmbg import ImageSegmentation
    from ai_tools.modnet import MODNetSegmentation
    from ai_tools.brn import BiRefNetAPP
    from ai_tools.ppocr_tool import PPOCR
except Exception as e:
    base_dir = Path(__file__).parent.parent
    sys.path.append(str(base_dir))
    from utilities.log import logger

    from utilities.utils import (
        image_obj_to_base64,
        is_image,
        save_bs64_image,
        add_stroke,
        crop_main_subject,
        read_image,
        get_image_format,
        process_gif,
    )
    from ai_tools.rmbg import ImageSegmentation
    from ai_tools.modnet import MODNetSegmentation
    from ai_tools.brn import BiRefNetAPP
    from ai_tools.ppocr_tool import PPOCR

    logger.error(f"Error in import: {e}")


def swagger_monkey_patch(*args, **kwargs):
    return get_swagger_ui_html(
        *args,
        **kwargs,
        swagger_js_url="https://cdn.staticfile.net/swagger-ui/5.9.0/swagger-ui-bundle.min.js",
        swagger_css_url="https://cdn.staticfile.net/swagger-ui/5.9.0/swagger-ui.min.css",
    )


applications.get_swagger_ui_html = swagger_monkey_patch

app = FastAPI(
    title="灵象工具箱 API",
    description="灵象工具箱 API",
    version="1.0.0",
    docs_url="/api/docs",  # Custom URL for Swagger documentation
    redoc_url="/api/redoc",  # Custom URL for ReDoc documentationI
)

# 配置CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)


# 添加静态文件服务，禁用缓存
@app.get("/assets/{file_path:path}", include_in_schema=False)
async def serve_assets(file_path: str):
    """
    提供assets目录下的静态文件服务，确保JS和CSS不被缓存
    """
    try:
        root_dir = os.path.join(
            os.path.dirname(os.path.dirname(os.path.abspath(__file__))), "web", "assets"
        )
        file_path = os.path.join(root_dir, file_path)

        if not os.path.exists(file_path) or not os.path.isfile(file_path):
            raise HTTPException(status_code=404, detail="Static file not found")

        mime_type, _ = mimetypes.guess_type(file_path)
        if not mime_type:
            mime_type = "application/octet-stream"

        headers = {
            "Content-Type": mime_type,
            "Cache-Control": "no-store, no-cache, must-revalidate, max-age=0",
            "Pragma": "no-cache",
            "Expires": "0",
        }

        async def file_iterator():
            with open(file_path, mode="rb") as file:
                while chunk := file.read(8192):
                    yield chunk

        return StreamingResponse(file_iterator(), headers=headers)
    except Exception as e:
        logger.error(f"Error serving static file: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.get("/logo.png", include_in_schema=False)
async def serve_logo():
    """
    提供logo图标
    """
    try:
        logo_path = os.path.join(
            os.path.dirname(os.path.dirname(os.path.abspath(__file__))),
            "web",
            "logo.png",
        )

        if not os.path.exists(logo_path) or not os.path.isfile(logo_path):
            raise HTTPException(status_code=404, detail="Logo file not found")

        headers = {
            "Content-Type": "image/png",
            "Cache-Control": "public, max-age=86400",  # 图标可以缓存
        }

        async def file_iterator():
            with open(logo_path, mode="rb") as file:
                while chunk := file.read(8192):
                    yield chunk

        return StreamingResponse(file_iterator(), headers=headers)
    except Exception as e:
        logger.error(f"Error serving logo: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.get("/vite.svg", include_in_schema=False)
async def serve_vite_svg():
    """
    提供vite svg图标
    """
    try:
        svg_path = os.path.join(
            os.path.dirname(os.path.dirname(os.path.abspath(__file__))),
            "web",
            "vite.svg",
        )

        if not os.path.exists(svg_path) or not os.path.isfile(svg_path):
            raise HTTPException(status_code=404, detail="SVG file not found")

        headers = {
            "Content-Type": "image/svg+xml",
            "Cache-Control": "public, max-age=86400",  # 图标可以缓存
        }

        async def file_iterator():
            with open(svg_path, mode="rb") as file:
                while chunk := file.read(8192):
                    yield chunk

        return StreamingResponse(file_iterator(), headers=headers)
    except Exception as e:
        logger.error(f"Error serving SVG: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.get("/", include_in_schema=False)
@app.get("/index.html", include_in_schema=False)
async def serve_index_html():
    """
    提供index.html，处理根路径请求
    """
    try:
        index_path = os.path.join(
            os.path.dirname(os.path.dirname(os.path.abspath(__file__))),
            "web",
            "index.html",
        )

        if not os.path.exists(index_path) or not os.path.isfile(index_path):
            raise HTTPException(status_code=404, detail="Index file not found")

        headers = {
            "Content-Type": "text/html; charset=utf-8",
            "Cache-Control": "no-store, no-cache, must-revalidate, max-age=0",
            "Pragma": "no-cache",
            "Expires": "0",
        }

        async def file_iterator():
            with open(index_path, mode="rb") as file:
                while chunk := file.read(8192):
                    yield chunk

        return StreamingResponse(file_iterator(), headers=headers)
    except Exception as e:
        logger.error(f"Error serving index: {e}")
        raise HTTPException(status_code=500, detail=str(e))


# 修改流式响应，支持所有类型的媒体文件
@app.get("/local_file", include_in_schema=False)
async def get_local_file(file_path: str, request: Request):
    """
    流式响应本地文件，支持范围请求
    """
    try:
        decoded_path = unquote(file_path)
        normalized_path = os.path.normpath(decoded_path)

        if not os.path.exists(normalized_path) or not os.path.isfile(normalized_path):
            raise HTTPException(status_code=404, detail="File not found")

        file_size = os.path.getsize(normalized_path)
        mime_type, _ = mimetypes.guess_type(normalized_path)
        # 如果无法猜测 MIME 类型，返回通用的二进制流类型
        if not mime_type:
            mime_type = "application/octet-stream"

        # 处理范围请求
        range_header = request.headers.get("range")

        headers = {
            "Accept-Ranges": "bytes",
            "Content-Type": mime_type,
            "Access-Control-Allow-Origin": "*",
            "Access-Control-Allow-Methods": "GET, OPTIONS",
            "Access-Control-Allow-Headers": "Range",
            "Access-Control-Expose-Headers": "Content-Range, Content-Length",
        }

        if range_header:
            start_byte = 0
            end_byte = file_size - 1

            if range_header.startswith("bytes="):
                range_data = range_header.split("=")[1]
                ranges = range_data.split("-")

                if ranges[0]:
                    start_byte = int(ranges[0])
                if ranges[1]:
                    end_byte = int(ranges[1])

            # 计算实际大小
            chunk_size = end_byte - start_byte + 1

            headers["Content-Range"] = f"bytes {start_byte}-{end_byte}/{file_size}"
            headers["Content-Length"] = str(chunk_size)

            async def range_iterator():
                with open(normalized_path, mode="rb") as file:
                    file.seek(start_byte)
                    remaining = chunk_size
                    while remaining > 0:
                        chunk_size = min(8192, remaining)
                        data = file.read(chunk_size)
                        if not data:
                            break
                        remaining -= len(data)
                        yield data

            return StreamingResponse(range_iterator(), status_code=206, headers=headers)
        else:
            # 完整文件请求
            headers["Content-Length"] = str(file_size)

            async def file_iterator():
                with open(normalized_path, mode="rb") as file:
                    while chunk := file.read(8192):
                        yield chunk

            return StreamingResponse(file_iterator(), headers=headers)

    except Exception as e:
        logger.error(f"Error serving file: {e}")
        raise HTTPException(status_code=500, detail=str(e))


class MattingRequest(BaseModel):
    """
    抠图请求
    """

    image_path: str = Field(description="图片路径，base64值或者图片url")
    ai_model: str = Field(
        default="fast",
        choices=["person", "fast", "normal", "quality"],
        description="抠图模型: [person, fast, normal, quality]",
    )
    is_edge_optimization: bool = Field(default=False, description="是否启用边缘优化")
    edge_value: int = Field(default=90, description="边缘推荐，范围10-100")


class BaseResponse(BaseModel):
    """
    基础响应
    """

    code: int = Field(default=200, description="状态码: [200, 500]")
    message: str = Field(default="success", description="状态信息")
    data: Union[dict, None] = Field(default=None, description="数据")


class MattingData(BaseModel):
    """
    抠图数据
    """

    no_bg_image: str = Field(default="", description="抠图后的图片Base64值")


class MattingResponse(BaseResponse):
    """
    抠图响应
    """

    data: Union[MattingData, None] = Field(default=None, description="抠图数据")


def _get_segment_func(ai_model):
    """获取对应的分割函数"""
    if ai_model == "person":
        return MODNetSegmentation().segment_image
    elif ai_model == "fast":
        return ImageSegmentation().segment_image
    elif ai_model == "normal":
        return BiRefNetAPP(model_type="tiny_232").segment_image
    elif ai_model == "quality":
        return BiRefNetAPP(model_type="epoch_100").segment_image
    elif ai_model == "rmbg2":
        return BiRefNetAPP(model_type="rmbg2").segment_image
    else:
        raise ValueError(
            f"Invalid ai_model: {ai_model}, please choose from [person, fast, normal, quality, rmbg2]"
        )


@app.post(
    "/api/image/matting", response_model=MattingResponse, tags=["抠图"], summary="图像抠图"
)
async def matting(request: MattingRequest):
    """
    图像抠图
    """
    try:
        image_path = request.image_path
        segment_func = _get_segment_func(request.ai_model)
        no_bg_image = segment_func(
            image_path,
            is_edge_optimization=request.is_edge_optimization,
            edge_value=request.edge_value,
        )
        no_bg_image = image_obj_to_base64(no_bg_image)
        return MattingResponse(data=MattingData(no_bg_image=no_bg_image))
    except Exception as e:
        error_msg = f"Error in matting: {str(e)}"
        logger.error(error_msg)
        return MattingResponse(code=500, message=error_msg, data=None)


class OCRRequest(BaseModel):
    """
    OCR请求
    """

    image_path: str = Field(description="图片路径，base64值或者图片url")


class OCRData(BaseModel):
    """
    OCR数据
    """

    ocr_result: str = Field(default="", description="OCR结果")


class OCRResponse(BaseResponse):
    """
    OCR响应
    """

    data: Union[OCRData, None] = Field(default=None, description="OCR数据")


@app.post("/api/ocr", response_model=OCRResponse, tags=["OCR"], summary="OCR")
async def ocr(request: OCRRequest):
    """
    OCR
    """
    try:
        image_path = request.image_path
        image, _ = read_image(image_path)
        # 确保图像是 RGB 格式（3通道）
        if image.mode == "RGBA":
            image = image.convert("RGB")
        ndarray = np.array(image)
        result = PPOCR().ocr(ndarray, cls=True)

        # 从结果中提取文字
        if result and isinstance(result, tuple) and len(result) >= 2:
            texts = [text_tuple[0] for text_tuple in result[1]]
            result_txt = "\n".join(texts)
        else:
            result_txt = ""
        return OCRResponse(data=OCRData(ocr_result=result_txt))
    except Exception as e:
        error_msg = f"Error in ppocr: {str(e)}"
        logger.error(error_msg)
        return OCRResponse(code=500, message=error_msg, data=None)


class APIServer:
    def __init__(self, host: str = "127.0.0.1", port: int = 11112):
        self.host = host
        self.port = port
        self.server_thread: Optional[threading.Thread] = None
        self.should_exit = threading.Event()
        self.base_url = f"http://{host}:{port}"
        self.temp_dir = tempfile.mkdtemp(prefix="api_server_")
        self.server = None

    def start(self):
        """在新线程中启动FastAPI服务器"""
        self.server_thread = threading.Thread(target=self._run_server, daemon=True)
        self.server_thread.start()

    def _run_server(self):
        """运行FastAPI服务器"""
        config = uvicorn.Config(
            app=app,
            host=self.host,
            port=self.port,
            limit_concurrency=1000,
            timeout_keep_alive=30,
            log_level="info",
        )
        self.server = uvicorn.Server(config)
        self.server.run()

    def stop(self):
        """停止FastAPI服务器（同步版本）"""
        logger.info("Stopping FastAPI server")
        if self.server:
            self.server.should_exit = True

        if self.server_thread:
            self.server_thread.join(timeout=1.0)

        # 清理临时目录
        try:
            if os.path.exists(self.temp_dir):
                shutil.rmtree(self.temp_dir, ignore_errors=True)
        except Exception as e:
            logger.error(f"Error cleaning up temp dir: {e}")
        logger.info("FastAPI server stopped")


if __name__ == "__main__":
    server = APIServer(port=11113)
    server.start()
    try:
        server.server_thread.join()
    except KeyboardInterrupt:
        server.stop()
