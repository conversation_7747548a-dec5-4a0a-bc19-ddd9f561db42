from utilities.response import res500, res200, res400
from utilities.convert_utils import (
    convert_image_format,
    can_convert_file,
    gif_to_image,
    get_file_base_name,
)
from utilities.log import logger
from ai_tools.utils import get_api_server_url
from utilities.utils import img_to_base64
import time
import os


class ConvertImageAPI:
    name = "convert_image"

    def single_convert_image(self, playload):
        try:
            input_path = playload["input_path"]
            output_format = playload["output_format"]
            quality = playload.get("quality", None)
        except KeyError:
            return res500("Invalid input")
        try:
            base_name = get_file_base_name(input_path)
        except Exception as e:
            base_name = f"lxt"
        if input_path.lower().endswith(".gif"):
            filename = f"{base_name}-GIF"
        else:
            filename = f"{base_name}-convert.{output_format.lower()}"
        result = self.open_save_dialog(
            filename,
        )
        if not result:
            return res200(msg="Cancel")
        try:
            if input_path.lower().endswith(".gif"):
                flag = gif_to_image(input_path, output_format, result)
            else:
                flag = convert_image_format(input_path, output_format, result, quality)
        except Exception as e:
            flag = False
            logger.error(f"convert image {input_path} to {result} failed: {e}")
        if flag:
            logger.info(f"convert image {input_path} to {result} success")
            return res200(data={"output_path": result})
        else:
            return res400(msg="Cancel")

    def get_folder_convert_images(self, folder_path: str) -> dict:
        """
        Get the list of images in the input folder.


        :param folder_path: The path of the input folder.

        :return: The response object with the list of images in the folder.

        """
        try:
            convert_images = []
            for root, dirs, files in os.walk(folder_path):
                for file in files:
                    if can_convert_file(file):
                        image_path = os.path.join(root, file)
                        if (
                            image_path.lower().endswith(".webp")
                            or image_path.lower().endswith(".tiff")
                            or image_path.lower().endswith(".ico")
                            or image_path.lower().endswith(".icns")
                        ):
                            image_url = img_to_base64(image_path)
                        else:
                            image_url = f"{get_api_server_url()}/local_file?file_path={image_path}"
                        tem = {
                            "image_name": os.path.basename(image_path),
                            "image_path": image_path,
                            "image_url": image_url,
                            "status": "",
                            "convert_result": "",
                        }
                        convert_images.append(tem)
            return res200(
                data={"convert_images": convert_images, "folder_path": folder_path}
            )
        except Exception as e:
            logger.error(f"Error in get_folder_convert_images: {e} {folder_path}")
            return res500("Error in get_folder_convert_images")

    def convert_image_from_folder(self, playload):
        try:
            image_path = playload.get("image_path")
            folder_path = playload.get("folder_path")
            output_format = playload["output_format"]
            quality = playload.get("quality", None)
            save_folder = playload.get("save_folder")
            if not save_folder:
                save_folder = os.path.join(folder_path, "result")
            if not os.path.exists(save_folder):
                os.makedirs(save_folder, exist_ok=True)
            try:
                base_name = get_file_base_name(image_path)
            except Exception as e:
                base_name = f"lxt-{int(time.time()*1000)}"
            if image_path.lower().endswith(".gif") and output_format.lower() != "gif":
                filename = f"{base_name}-GIF"
            else:
                filename = f"{base_name}.{output_format.lower()}"
            save_path = os.path.join(save_folder, filename)
            try:
                if (
                    image_path.lower().endswith(".gif")
                    and output_format.lower() != "gif"
                ):
                    flag = gif_to_image(image_path, output_format, save_path)
                else:
                    flag = convert_image_format(
                        image_path, output_format, save_path, quality
                    )
            except Exception as e:
                flag = False
                logger.error(f"convert image {image_path} to {save_path} failed: {e}")
            if flag:
                logger.info(f"convert image {image_path} to {save_path} success")
                return res200(data={"convert_result": save_path})
            else:
                return res500(msg="convert image failed")

        except Exception as e:
            logger.error(f"Error in convert image: {e} {image_path}")
            return res500("Error in convert image")
